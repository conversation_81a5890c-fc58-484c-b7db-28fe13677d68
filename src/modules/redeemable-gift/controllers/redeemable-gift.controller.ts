import { Controller, Get, Post, Put, Delete, Query, Param, Body, UseGuards, ParseIntPipe } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiResponse, ApiBody, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../../common/guards/index.js';
import { CreateRedeemableGiftDto, UpdateRedeemableGiftDto, RedeemableGiftDto } from '../dto/index.js';
import { RedeemableGiftService } from '../services/index.js';

@Controller('redeemable-gifts')
@ApiTags('Redeemable Gifts')
@ApiBearerAuth('bearer')
@UseGuards(JwtAuthGuard)
export class RedeemableGiftController {
  constructor(private readonly redeemableGiftService: RedeemableGiftService) {}

  @Get()
  @ApiOperation({ summary: 'Get all redeemable gifts with pagination and search' })
  @ApiResponse({ status: 200, description: 'List of redeemable gifts', type: RedeemableGiftDto, isArray: true })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search by name or type' })
  public async findAll(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search?: string,
  ): Promise<{ gifts: RedeemableGiftDto[]; total: number }> {
    return this.redeemableGiftService.findAll(page, limit, search);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new redeemable gift with products (batch create)' })
  @ApiBody({ type: CreateRedeemableGiftDto })
  @ApiResponse({ status: 201, description: 'Gift created successfully with all products', type: RedeemableGiftDto })
  public async create(@Body() createDto: CreateRedeemableGiftDto): Promise<RedeemableGiftDto> {
    return this.redeemableGiftService.create(createDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a redeemable gift (gift info and/or products)' })
  @ApiParam({ name: 'id', type: Number, description: 'Gift ID' })
  @ApiBody({ type: UpdateRedeemableGiftDto })
  @ApiResponse({ status: 200, description: 'Gift updated successfully', type: RedeemableGiftDto })
  @ApiResponse({ status: 404, description: 'Gift not found' })
  public async update(@Param('id', ParseIntPipe) id: number, @Body() updateDto: UpdateRedeemableGiftDto): Promise<RedeemableGiftDto> {
    return this.redeemableGiftService.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a redeemable gift and all associated products (batch delete)' })
  @ApiParam({ name: 'id', type: Number, description: 'Gift ID' })
  @ApiResponse({ status: 200, description: 'Gift and all products deleted successfully' })
  @ApiResponse({ status: 404, description: 'Gift not found' })
  public async delete(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    await this.redeemableGiftService.delete(id);
    return { message: 'Gói quà và tất cả sản phẩm đã được xóa thành công' };
  }

  @Put(':id/toggle-active')
  @ApiOperation({ summary: 'Toggle active status of a redeemable gift' })
  @ApiParam({ name: 'id', type: Number, description: 'Gift ID' })
  @ApiResponse({ status: 200, description: 'Gift status toggled successfully', type: RedeemableGiftDto })
  @ApiResponse({ status: 404, description: 'Gift not found' })
  public async toggleActive(@Param('id', ParseIntPipe) id: number): Promise<RedeemableGiftDto> {
    return this.redeemableGiftService.toggleActive(id);
  }
}
