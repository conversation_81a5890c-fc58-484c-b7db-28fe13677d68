import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Module } from '@nestjs/common';

import { RedeemableGiftController } from './controllers/redeemable-gift.controller.js';
import { RedeemableGiftService } from './services/index.js';
import { RedeemableGift, RedeemableVendureProduct } from '../../entities/index.js';

@Module({
  imports: [MikroOrmModule.forFeature([RedeemableGift, RedeemableVendureProduct])],
  controllers: [RedeemableGiftController],
  providers: [RedeemableGiftService],
  exports: [RedeemableGiftService],
})
export class RedeemableGiftModule {}
