import type { FilterQuery } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/mysql';
import { Injectable, NotFoundException } from '@nestjs/common';

import { RedeemableGift, RedeemableGiftRepository, RedeemableVendureProduct } from '../../../entities/index.js';
import {
  CreateRedeemableGiftDto,
  UpdateRedeemableGiftDto,
  RedeemableGiftDto,
  RedeemableProductInGiftDto,
  UpdateRedeemableProductInGiftDto,
} from '../dto/index.js';

@Injectable()
export class RedeemableGiftService {
  constructor(
    private readonly redeemableGiftRepository: RedeemableGiftRepository,
    private readonly em: EntityManager,
  ) {}

  public async findAll(page: number, limit: number, search?: string): Promise<{ gifts: RedeemableGiftDto[]; total: number }> {
    let where: FilterQuery<RedeemableGift> = {};
    if (search) {
      where = {
        $or: [{ name: { $like: `%${search}%` } }, { type: { $like: `%${search}%` } }],
      };
    }

    const [gifts, total] = await this.redeemableGiftRepository.findAndCount(where, {
      offset: (page - 1) * limit,
      limit,
      orderBy: { createdAt: 'DESC' },
      populate: ['products'],
    });

    const mapped = this.mapToDto(gifts);
    return { gifts: mapped, total };
  }

  public async create(createDto: CreateRedeemableGiftDto): Promise<RedeemableGiftDto> {
    // Create the gift wrapper
    const gift = this.redeemableGiftRepository.create({
      type: createDto.type,
      name: createDto.name,
      description: createDto.description,
      isActive: createDto.isActive ?? true,
    });

    // Create associated products
    const products: RedeemableVendureProduct[] = [];
    for (const productDto of createDto.products) {
      const product = this.em.create(RedeemableVendureProduct, {
        productId: productDto.productId,
        variantId: productDto.variantId,
        variantName: productDto.variantName,
        variantPrice: productDto.variantPrice,
        pointsCost: productDto.pointsCost,
        imageUrl: productDto.imageUrl,
        sku: productDto.sku,
        redeemableGift: gift,
      });
      products.push(product);
    }

    gift.products.set(products);
    await this.em.persistAndFlush(gift);

    // Reload with populated products
    await this.em.populate(gift, ['products']);
    return this.mapToDto([gift])[0];
  }

  public async update(id: number, updateDto: UpdateRedeemableGiftDto): Promise<RedeemableGiftDto> {
    const gift = await this.redeemableGiftRepository.findOne({ id }, { populate: ['products'] });
    if (!gift) {
      throw new NotFoundException('Không tìm thấy gói quà có thể đổi');
    }

    if (updateDto.type !== undefined) gift.type = updateDto.type;
    if (updateDto.name !== undefined) gift.name = updateDto.name;
    if (updateDto.description !== undefined) gift.description = updateDto.description;
    if (updateDto.isActive !== undefined) gift.isActive = updateDto.isActive;

    if (updateDto.products) {
      this.updateGiftProducts(gift, updateDto.products);
    }

    await this.em.flush();

    await this.em.populate(gift, ['products'], { refresh: true });
    return this.mapToDto([gift])[0];
  }

  public async delete(id: number): Promise<void> {
    const gift = await this.redeemableGiftRepository.findOne({ id }, { populate: ['products'] });
    if (!gift) {
      throw new NotFoundException('Không tìm thấy gói quà có thể đổi');
    }

    // Due to orphanRemoval: true, associated products will be automatically deleted
    await this.em.removeAndFlush(gift);
  }

  public async toggleActive(id: number): Promise<RedeemableGiftDto> {
    const gift = await this.redeemableGiftRepository.findOne({ id }, { populate: ['products'] });
    if (!gift) {
      throw new NotFoundException('Không tìm thấy gói quà có thể đổi');
    }

    gift.isActive = !gift.isActive;
    await this.em.flush();
    return this.mapToDto([gift])[0];
  }

  private mapToDto(gifts: RedeemableGift[]): RedeemableGiftDto[] {
    return gifts.map((gift) => ({
      id: gift.id,
      type: gift.type,
      name: gift.name,
      description: gift.description,
      isActive: gift.isActive,
      products: this.mapProductsToDto(gift.products.getItems()),
    }));
  }

  private mapProductsToDto(products: RedeemableVendureProduct[]): RedeemableProductInGiftDto[] {
    return products.map((product) => ({
      id: product.id,
      productId: product.productId,
      variantId: product.variantId,
      variantName: product.variantName,
      variantPrice: product.variantPrice,
      pointsCost: product.pointsCost,
      imageUrl: product.imageUrl,
      sku: product.sku,
    }));
  }

  private updateGiftProducts(gift: RedeemableGift, productUpdates: UpdateRedeemableProductInGiftDto[]): void {
    const existingProducts = gift.products.getItems();
    const existingProductsMap = new Map(existingProducts.map((p) => [p.id, p]));

    const updatedProductIds = new Set<number>();

    for (const productUpdate of productUpdates) {
      if (productUpdate.id) {
        const existingProduct = existingProductsMap.get(productUpdate.id);
        if (existingProduct) {
          const previousPointsCost = existingProduct.pointsCost;
          const hasPointsCostChanged = previousPointsCost !== productUpdate.pointsCost;

          if (hasPointsCostChanged) {
            existingProduct.pointsCost = productUpdate.pointsCost;
          }

          updatedProductIds.add(productUpdate.id);
        }
      } else {
        const newProduct = this.em.create(RedeemableVendureProduct, {
          productId: productUpdate.productId,
          variantId: productUpdate.variantId,
          variantName: productUpdate.variantName,
          variantPrice: productUpdate.variantPrice,
          pointsCost: productUpdate.pointsCost,
          imageUrl: productUpdate.imageUrl,
          sku: productUpdate.sku,
          redeemableGift: gift,
        });

        gift.products.add(newProduct);
      }
    }

    const productsToRemove = existingProducts.filter((p) => !updatedProductIds.has(p.id));
    for (const productToRemove of productsToRemove) {
      gift.products.remove(productToRemove);
      this.em.remove(productToRemove);
    }
  }
}
