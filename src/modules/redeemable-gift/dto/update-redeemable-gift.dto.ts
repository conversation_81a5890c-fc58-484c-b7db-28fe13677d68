import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsOptional, MaxLength, IsBoolean, IsArray, ValidateNested, IsNotEmpty, IsNumber } from 'class-validator';

export class UpdateRedeemableProductInGiftDto {
  @ApiProperty({ example: 1, description: 'Product ID (only for existing products, omit for new products)', required: false })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({ example: '12', description: 'Vendure product ID' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  productId!: string;

  @ApiProperty({ example: '23', description: 'Vendure variant ID' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  variantId!: string;

  @ApiProperty({ example: 'Aqualic BHA 2% Herbal Toner (200ml)', description: 'Product variant name' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  variantName!: string;

  @ApiProperty({ example: 80000, description: 'Product price in VND', required: false })
  @IsOptional()
  variantPrice?: number;

  @ApiProperty({ example: 80, description: 'Points required for redemption' })
  @IsNotEmpty()
  pointsCost!: number;

  @ApiProperty({ example: 'https://example.com/image.jpg', description: 'Product image URL', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  imageUrl?: string;

  @ApiProperty({ example: 'DST-DC-012', description: 'Product SKU', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  sku?: string;
}

export class UpdateRedeemableGiftDto {
  @ApiProperty({ example: 'Đổi sản phẩm', description: 'Gift type', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(30)
  type?: string;

  @ApiProperty({ example: 'Updated gift name', description: 'Gift name/title', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(70)
  name?: string;

  @ApiProperty({ example: 'Updated description', description: 'Gift description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: false, description: 'Whether the gift is active', required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    type: [UpdateRedeemableProductInGiftDto],
    description: 'List of products to include in this gift package. Existing products should include id, new products should omit id.',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateRedeemableProductInGiftDto)
  products?: UpdateRedeemableProductInGiftDto[];
}
