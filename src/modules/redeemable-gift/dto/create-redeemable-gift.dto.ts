import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional, IsArray, ValidateNested, MaxLength, IsBoolean } from 'class-validator';

export class CreateRedeemableProductInGiftDto {
  @ApiProperty({ example: '12', description: 'Vendure product ID' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  productId!: string;

  @ApiProperty({ example: '23', description: 'Vendure variant ID' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  variantId!: string;

  @ApiProperty({ example: 'Aqualic BHA 2% Herbal Toner (200ml)', description: 'Product variant name' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  variantName!: string;

  @ApiProperty({ example: 80000, description: 'Product price in VND', required: false })
  @IsOptional()
  variantPrice?: number;

  @ApiProperty({ example: 80, description: 'Points required for redemption' })
  @IsNotEmpty()
  pointsCost!: number;

  @ApiProperty({ example: 'https://example.com/image.jpg', description: 'Product image URL', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  imageUrl?: string;

  @ApiProperty({ example: 'DST-DC-012', description: 'Product SKU', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  sku?: string;
}

export class CreateRedeemableGiftDto {
  @ApiProperty({ example: 'Đổi sản phẩm', description: 'Gift type' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(30)
  type!: string;

  @ApiProperty({ example: 'Tối đa 70 ký tự', description: 'Gift name/title' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(70)
  name!: string;

  @ApiProperty({ example: 'Description of the gift package', description: 'Gift description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: true, description: 'Whether the gift is active', required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    type: [CreateRedeemableProductInGiftDto],
    description: 'List of products to include in this gift package',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateRedeemableProductInGiftDto)
  products!: CreateRedeemableProductInGiftDto[];
}
