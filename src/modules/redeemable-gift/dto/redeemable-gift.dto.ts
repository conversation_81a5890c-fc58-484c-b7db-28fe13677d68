import { ApiProperty } from '@nestjs/swagger';

export class RedeemableProductInGiftDto {
  @ApiProperty({ example: 1, description: 'Product ID' })
  id!: number;

  @ApiProperty({ example: 'DST-DC-012', description: 'Vendure product ID' })
  productId!: string;

  @ApiProperty({ example: 'variant-123', description: 'Vendure variant ID' })
  variantId!: string;

  @ApiProperty({ example: 'Aqualic BHA 2% Herbal Toner (200ml)', description: 'Product variant name' })
  variantName!: string;

  @ApiProperty({ example: 80000, description: 'Product price in VND', nullable: true })
  variantPrice?: number;

  @ApiProperty({ example: 80, description: 'Points required for redemption' })
  pointsCost!: number;

  @ApiProperty({ example: 'https://example.com/image.jpg', description: 'Product image URL', nullable: true })
  imageUrl?: string;

  @ApiProperty({ example: 'DST-DC-012', description: 'Product SKU', nullable: true })
  sku?: string;
}

export class RedeemableGiftDto {
  @ApiProperty({ example: 1, description: 'Gift ID' })
  id!: number;

  @ApiProperty({ example: 'Đổi sản phẩm', description: 'Gift type' })
  type!: string;

  @ApiProperty({ example: 'Tối đa 70 ký tự', description: 'Gift name/title' })
  name!: string;

  @ApiProperty({ example: 'Description of the gift package', description: 'Gift description', nullable: true })
  description?: string;

  @ApiProperty({ example: true, description: 'Whether the gift is active' })
  isActive!: boolean;

  @ApiProperty({ type: [RedeemableProductInGiftDto], description: 'Products included in this gift package' })
  products!: RedeemableProductInGiftDto[];
}
