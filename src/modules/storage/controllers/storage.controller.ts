import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard.js';
import { GetPresignedDto } from '../dto/index.js';
import { StorageService } from '../services/index.js';

@Controller('storage')
@ApiTags('Storage')
@ApiBearerAuth('bearer')
@UseGuards(JwtAuthGuard)
export class StorageController {
  constructor(private readonly storageService: StorageService) {}

  @Post('put-object-url')
  @ApiOperation({ summary: 'Get presigned URL for file upload' })
  @ApiBody({ type: GetPresignedDto })
  @ApiResponse({
    status: 200,
    description: 'Presigned URL generated successfully',
    schema: { type: 'string' },
  })
  public async register(@Body() dto: GetPresignedDto): Promise<string> {
    return this.storageService.getUploadUrl(dto);
  }
}
