import type { FilterQuery } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/mysql';
import { Injectable, NotFoundException } from '@nestjs/common';

import { ProductPoint, ProductPointRepository, User } from '../../../entities/index.js';
import { ProductPointListDto } from '../dto/index.js';

@Injectable()
export class ProductService {
  constructor(
    private readonly productPointRepository: ProductPointRepository,
    private readonly em: EntityManager,
  ) {}

  public async find(page: number, limit: number, search?: string): Promise<{ products: ProductPointListDto[]; total: number }> {
    let where: FilterQuery<ProductPoint> = {};
    if (search) {
      where = {
        $or: [{ sku: { $like: `%${search}%` } }, { name: { $like: `%${search}%` } }],
      };
    }
    const [products, total] = await this.productPointRepository.findAndCount(where, {
      offset: (page - 1) * limit,
      limit,
      orderBy: { createdAt: 'DESC' },
      populate: ['updatedBy'],
    });
    const mapped = this.mapProductPointsToDto(products);
    return { products: mapped, total };
  }

  public async updateProduct(id: number, updateData: { name?: string; price?: number; point?: number }, user: User): Promise<ProductPointListDto> {
    const product = await this.productPointRepository.findOne({ id });
    if (!product) {
      throw new NotFoundException('Không tìm thấy sản phẩm');
    }
    
    if (updateData.name !== undefined) {
      product.name = updateData.name;
    }
    if (updateData.price !== undefined) {
      product.price = updateData.price;
    }
    if (updateData.point !== undefined) {
      product.point = updateData.point;
    }
    
    product.updatedBy = user;
    await this.em.flush();
    return this.mapProductPointsToDto([product])[0];
  }

  private mapProductPointsToDto(products: ProductPoint[]): ProductPointListDto[] {
    return products.map((product) => ({
      id: product.id,
      sku: product.sku,
      name: product.name,
      price: product.price,
      point: product.point,
      updatedBy: product.updatedBy ? { id: product.updatedBy.id, fullName: product.updatedBy.fullName ?? '' } : null,
      updatedAt: product.updatedAt,
    }));
  }

  public async getUserById(id: number): Promise<User | null> {
    return this.em.getRepository(User).findOne({ id });
  }
}
