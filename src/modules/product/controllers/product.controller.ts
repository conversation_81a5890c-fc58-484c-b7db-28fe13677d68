import { Controller, Get, Query, UseGuards, Body, Param, Patch } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiResponse, ApiBody, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';

import { GetUser } from '../../../common/decorators/get-user.decorator.js';
import { JwtAuthGuard } from '../../../common/guards/index.js';
import { User } from '../../../entities/index.js';
import { ProductPointListDto } from '../dto/index.js';
import { ProductService } from '../services/product.service.js';

@Controller('products')
@ApiTags('Product')
@ApiBearerAuth('bearer')
@UseGuards(JwtAuthGuard)
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Get()
  @ApiOperation({ summary: 'Get a list of products' })
  @ApiResponse({ status: 200, description: 'List of products', type: ProductPointListDto, isArray: true })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search by SKU or name (partial match)' })
  public find(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search?: string,
  ): Promise<{ products: ProductPointListDto[]; total: number }> {
    return this.productService.find(page, limit, search);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update product name, price and point' })
  @ApiParam({ name: 'id', type: Number, description: 'ProductPoint id' })
  @ApiBody({
    schema: { 
      properties: { 
        name: { type: 'string', example: 'Updated Product Name' },
        price: { type: 'number', example: 150000 },
        point: { type: 'number', example: 10 } 
      } 
    } 
  })
  @ApiResponse({ status: 200, description: 'Updated product', type: ProductPointListDto })
  public updateProduct(
    @GetUser() user: User, 
    @Param('id') id: number, 
    @Body() updateData: { name?: string; price?: number; point?: number }
  ): Promise<ProductPointListDto> {
    return this.productService.updateProduct(id, updateData, user);
  }
}
