import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Module } from '@nestjs/common';

import { ProductController } from './controllers/product.controller.js';
import { ProductService } from './services/product.service.js';
import { ProductPoint } from '../../entities/index.js';

@Module({
  imports: [MikroOrmModule.forFeature([ProductPoint])],
  controllers: [ProductController],
  providers: [ProductService],
})
export class ProductModule {}
