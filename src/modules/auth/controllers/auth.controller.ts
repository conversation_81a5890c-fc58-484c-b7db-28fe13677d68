import { Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';
import type { FastifyRequest } from 'fastify';

import { LocalAuthGuard } from '../../../common/guards/local-auth.guard.js';
import { UserLoginDto } from '../dto/index.js';
import { AuthService } from '../services/index.js';

@Controller('auth')
@ApiTags('Auth')
export class AuthController {
  // private readonly logger: Logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @UseGuards(LocalAuthGuard)
  @ApiOperation({ summary: 'User login' })
  @ApiBody({ type: UserLoginDto })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    schema: {
      properties: {
        accessToken: { type: 'string' },
      },
    },
  })
  public login(@Req() req: FastifyRequest): { accessToken: string } {
    const user = req.user;

    return {
      accessToken: this.authService.generateAccessToken(user),
    };
  }
}
