import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

import { UserService } from './user.service.js';
import { AuthErrorCode } from '../../../common/enums/index.js';
import { BadRequestException } from '../../../common/exceptions/index.js';
import type { User } from '../../../common/interfaces/user.interface.js';
import type { UserLoginDto } from '../dto/index.js';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private jwt: JwtService,
  ) {}

  public generateAccessToken(user: User): string {
    const payload = { sub: user.id, role: user.role };
    return this.jwt.sign(payload);
  }

  public async validateUserLogin(userLogin: UserLoginDto): Promise<User> {
    const user = await this.userService.findOneByEmail(userLogin.email);
    const isValid = await user?.comparePassword(userLogin.password);

    if (!user || !isValid) {
      throw new BadRequestException({
        code: AuthErrorCode.INVALID_CREDENTIALS,
        message: 'Thông tin đăng nhập không chính xác',
      });
    }

    return user;
  }
}
