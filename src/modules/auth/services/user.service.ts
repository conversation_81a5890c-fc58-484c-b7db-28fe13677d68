import { Injectable } from '@nestjs/common';

import { UserRoleEnum } from '../../../common/enums/index.js';
import { UserRepository } from '../../../entities/index.js';
import { User } from '../../../entities/user.entity.js';

@Injectable()
export class UserService {
  constructor(private readonly userRepository: UserRepository) {}

  async findOneByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({ email, role: UserRoleEnum.ADMIN });
  }

  async findOne(id: number): Promise<User | null> {
    return await this.userRepository.findOne({ id });
  }
}
