import type { FilterQuery } from '@mikro-orm/core';
import { Injectable, BadRequestException } from '@nestjs/common';
import ExcelJS from 'exceljs';

import { BooleanEnum } from '../../../common/enums/index.js';
import { UserPointHistoryRepository, ProductQrCodeRepository, ProductQrCode } from '../../../entities/index.js';
import { UserPointHistoryDto, ProductQrCodeListDto } from '../dto/index.js';

@Injectable()
export class PointManagementService {
  constructor(
    private readonly userPointHistoryRepository: UserPointHistoryRepository,
    private readonly productQrCodeRepository: ProductQrCodeRepository,
  ) {}

  public async getPointHistory(userId: number): Promise<UserPointHistoryDto[]> {
    const histories = await this.userPointHistoryRepository.find({ userId }, { orderBy: { createdAt: 'DESC' } });
    return histories.map((h) => ({
      id: h.id,
      userId: h.userId,
      itemName: h.itemName,
      points: h.points,
      redeemableItemId: h.redeemableItemId,
      productQrCodeId: h.productQrCodeId,
      createdAt: h.createdAt,
    }));
  }

  public async getProductQrCodesWithSku(
    page = 1,
    limit = 10,
    isUsed?: number,
    search?: string,
    usedAtFrom?: string,
    usedAtTo?: string,
  ): Promise<{ qrCodes: ProductQrCodeListDto[]; total: number }> {
    let where: FilterQuery<ProductQrCode> = { sku: { $ne: null } };
    if (isUsed !== undefined) {
      where.isUsed = isUsed;
    }
    if (search) {
      where = {
        ...where,
        $or: [
          { qrCode: { $like: `%${search}%` } },
          { scratchCode: { $like: `%${search}%` } },
          { sku: { $like: `%${search}%` } },
          { name: { $like: `%${search}%` } },
        ],
      };
    }

    if (usedAtFrom || usedAtTo) {
      const dateFilter: { $gte?: Date; $lte?: Date } = {};

      if (usedAtFrom) {
        dateFilter.$gte = new Date(usedAtFrom);
      }

      if (usedAtTo) {
        const toDate = new Date(usedAtTo);
        if (usedAtTo.length === 10) {
          toDate.setHours(23, 59, 59, 999);
        }
        dateFilter.$lte = toDate;
      }

      where.usedAt = dateFilter;
    }

    const [qrCodes, total] = await this.productQrCodeRepository.findAndCount(where, {
      offset: (page - 1) * limit,
      limit,
      orderBy: isUsed === 1 ? { usedAt: 'DESC' } : { createdAt: 'DESC' },
      populate: ['customer'],
      fields: [
        'qrCode',
        'scratchCode',
        'sku',
        'name',
        'price',
        'dispatchDate',
        'point',
        'isUsed',
        'usedAt',
        'batch',
        'expiresAt',
        'customer.phone',
        'customer.fullName',
      ],
    });

    return {
      qrCodes: qrCodes.map((qrCode) => ({
        ...qrCode,
        customer: qrCode.customer
          ? {
              phone: qrCode.customer.phone,
              fullName: qrCode.customer.fullName,
            }
          : undefined,
      })),
      total,
    };
  }

  public async exportProductQrCodesToExcel(isUsed?: number, search?: string, usedAtFrom?: string, usedAtTo?: string): Promise<{ buffer: Buffer; fileName: string }> {
    let where: FilterQuery<ProductQrCode> = { sku: { $ne: null } };

    if (isUsed !== undefined) {
      where.isUsed = isUsed;
    }

    if (search) {
      where = {
        ...where,
        $or: [
          { qrCode: { $like: `%${search}%` } },
          { scratchCode: { $like: `%${search}%` } },
          { sku: { $like: `%${search}%` } },
          { name: { $like: `%${search}%` } },
        ],
      };
    }

    if (usedAtFrom || usedAtTo) {
      const dateFilter: { $gte?: Date; $lte?: Date } = {};

      if (usedAtFrom) {
        dateFilter.$gte = new Date(usedAtFrom);
      }

      if (usedAtTo) {
        const toDate = new Date(usedAtTo);
        if (usedAtTo.length === 10) {
          toDate.setHours(23, 59, 59, 999);
        }
        dateFilter.$lte = toDate;
      }

      where.usedAt = dateFilter;
    }

    const qrCodes = await this.productQrCodeRepository.find(where, {
      orderBy: { createdAt: 'DESC' },
      populate: ['customer'],
      fields: [
        'qrCode',
        'scratchCode',
        'sku',
        'name',
        'price',
        'dispatchDate',
        'point',
        'isUsed',
        'usedAt',
        'batch',
        'expiresAt',
        'customer.phone',
        'customer.fullName',
      ],
    });

    if (qrCodes.length === 0) {
      throw new BadRequestException('No QR codes found for export.');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = `QR_Codes_Export_${timestamp}.xlsx`;

    const buffer = await this.generateExcelFileBuffer(<ProductQrCode[]>qrCodes);

    return { buffer, fileName };
  }

  private async generateExcelFileBuffer(qrCodes: ProductQrCode[]): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('QR Codes');

    worksheet.columns = [
      { header: 'No.', key: 'no', width: 8 },
      { header: 'QR Code', key: 'qrCode', width: 40 },
      { header: 'Scratch Code', key: 'scratchCode', width: 15 },
      { header: 'SKU', key: 'sku', width: 20 },
      { header: 'Product Name', key: 'name', width: 30 },
      { header: 'Price', key: 'price', width: 12 },
      { header: 'Points', key: 'point', width: 10 },
      { header: 'Used', key: 'isUsed', width: 8 },
      { header: 'Used At', key: 'usedAt', width: 20 },
      { header: 'Batch', key: 'batch', width: 15 },
      { header: 'Customer Phone', key: 'customerPhone', width: 15 },
      { header: 'Customer Name', key: 'customerName', width: 25 },
    ];

    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF4472C4' },
    };
    worksheet.getRow(1).font = { bold: true, color: { argb: 'FFFFFFFF' } };

    qrCodes.forEach((code, index) => {
      worksheet.addRow({
        no: index + 1,
        qrCode: code.qrCode,
        scratchCode: code.scratchCode,
        sku: code.sku ?? '',
        name: code.name ?? '',
        price: code.price ?? '',
        point: code.point ?? '',
        isUsed: code.isUsed === BooleanEnum.TRUE ? 'Yes' : 'No',
        usedAt: code.usedAt ? code.usedAt.toISOString().split('T')[0] : '',
        batch: code.batch ?? '',
        customerPhone: code.customer?.phone ?? '',
        customerName: code.customer?.fullName ?? '',
      });
    });

    worksheet.eachRow((row: ExcelJS.Row) => {
      row.eachCell((cell: ExcelJS.Cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });
    });

    worksheet.columns.forEach((column) => {
      if (column.header) {
        const lengths = qrCodes.map((row) => {
          const value = row[<keyof ProductQrCode>column.key];
          if (value === undefined) {
            return 0;
          }
          if (value instanceof Date) {
            return value.toISOString().length;
          }
          if (typeof value === 'string' || typeof value === 'number') {
            return String(value).length;
          }
          return 0;
        });
        const maxLength = Math.max(...lengths, String(column.header).length);
        column.width = Math.min(maxLength + 2, 50);
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  public async generateProductQrCodes(count: number): Promise<{ buffer: Buffer; fileName: string }> {
    if (count <= 0 || count > 500000) {
      throw new BadRequestException('Count must be between 1 and 500,000');
    }

    // Get existing QR codes and scratch codes to ensure uniqueness
    const existingQrCodes = new Set<string>();
    const existingScratchCodes = new Set<string>();

    // Fetch existing codes in batches to avoid memory issues
    const batchSize = 10000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const existingCodes = await this.productQrCodeRepository.find(
        {},
        {
          fields: ['qrCode', 'scratchCode'],
          offset,
          limit: batchSize,
        }
      );

      if (existingCodes.length === 0) {
        hasMore = false;
      } else {
        existingCodes.forEach(code => {
          existingQrCodes.add(code.qrCode);
          existingScratchCodes.add(code.scratchCode);
        });
        offset += batchSize;
      }
    }

    // Generate new unique codes
    const newCodes: Array<{ qrCode: string; scratchCode: string }> = [];
    const qrCodePrefix = 'dstlaboratory.vn/promo/DRC25';

    for (let i = 0; i < count; i++) {
      let qrCode: string;
      let scratchCode: string;

      // Generate unique QR code
      do {
        const suffix = this.generateRandomString(6); // 6 characters after DRC25
        qrCode = `${qrCodePrefix}${suffix}`;
      } while (existingQrCodes.has(qrCode));

      // Generate unique scratch code
      do {
        scratchCode = this.generateRandomNumericString(9); // 9 digit number
      } while (existingScratchCodes.has(scratchCode));

      // Add to sets to avoid duplicates within this generation
      existingQrCodes.add(qrCode);
      existingScratchCodes.add(scratchCode);

      newCodes.push({ qrCode, scratchCode });

      // Log progress every 10,000 records
      if ((i + 1) % 10000 === 0) {
        console.log(`Generated ${i + 1}/${count} codes`);
      }
    }

    // Generate CSV content
    const csvContent = this.generateCsvContent(newCodes);
    const buffer = Buffer.from(csvContent, 'utf8');

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = `Product_QR_Codes_${count}_${timestamp}.csv`;

    return { buffer, fileName };
  }

  private generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private generateRandomNumericString(length: number): string {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += Math.floor(Math.random() * 10).toString();
    }
    return result;
  }

  private generateCsvContent(codes: Array<{ qrCode: string; scratchCode: string }>): string {
    const headers = ['qrCode', 'scratchCode'];
    const csvRows = [headers.join(',')];

    codes.forEach(code => {
      csvRows.push(`"${code.qrCode}","${code.scratchCode}"`);
    });

    return csvRows.join('\n');
  }
}
