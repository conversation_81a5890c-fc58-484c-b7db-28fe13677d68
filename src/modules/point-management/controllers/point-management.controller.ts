import { Controller, Get, Param, UseGuards, Query, Res, Post } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import type { FastifyReply } from 'fastify';

import { JwtAuthGuard } from '../../../common/guards/index.js';
import { UserPointHistoryDto } from '../dto/index.js';
import type { ProductQrCodeListDto } from '../dto/index.js';
import { PointManagementService } from '../services/index.js';

@Controller('point-management')
@ApiTags('Point Management')
@ApiBearerAuth('bearer')
@UseGuards(JwtAuthGuard)
export class PointManagementController {
  constructor(private readonly pointManagementService: PointManagementService) {}

  @Get('point-history/:userId')
  @ApiOperation({ summary: "Get user's point history" })
  @ApiResponse({
    status: 200,
    description: 'Point history retrieved successfully',
    type: UserPointHistoryDto,
    isArray: true,
  })
  public getPointHistory(@Param('userId') userId: number): Promise<UserPointHistoryDto[]> {
    return this.pointManagementService.getPointHistory(userId);
  }

  @Get('product-qr-codes')
  @ApiOperation({
    summary:
      'Get paginated ProductQrCode with sku not null, filterable by isUsed (0/1), usedAt date range, and searchable by qrcode, scratchCode, sku, or name',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiQuery({ name: 'isUsed', required: false, type: Number, enum: [0, 1], description: 'Filter by isUsed (0 or 1)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search by QR code, scratch code, SKU, or name (partial match)' })
  @ApiQuery({
    name: 'usedAtFrom',
    required: false,
    type: String,
    description: 'Filter codes used from this date (YYYY-MM-DD or ISO string)',
  })
  @ApiQuery({
    name: 'usedAtTo',
    required: false,
    type: String,
    description: 'Filter codes used until this date (YYYY-MM-DD or ISO string)',
  })
  @ApiResponse({
    status: 200,
    description: 'Paginated ProductQrCode list',
    schema: {
      example: {
        qrCodes: [
          {
            id: 1,
            qrCode: 'QR123',
            sku: 'SKU123',
            name: 'Product',
            price: 100,
            point: 10,
            isUsed: 1,
            usedAt: '2024-01-15T10:30:00Z',
          },
        ],
        total: 1,
      },
    },
  })
  public async getProductQrCodesWithSku(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('isUsed') isUsed?: number,
    @Query('search') search?: string,
    @Query('usedAtFrom') usedAtFrom?: string,
    @Query('usedAtTo') usedAtTo?: string,
  ): Promise<{ qrCodes: ProductQrCodeListDto[]; total: number }> {
    const { qrCodes, total } = await this.pointManagementService.getProductQrCodesWithSku(
      page,
      limit,
      isUsed !== undefined ? Number(isUsed) : undefined,
      search,
      usedAtFrom,
      usedAtTo,
    );
    return { qrCodes: qrCodes, total };
  }

  @Get('product-qr-codes/export')
  @ApiOperation({
    summary: 'Export ProductQrCode data to Excel with same filtering options as the listing endpoint',
  })
  @ApiQuery({ name: 'isUsed', required: false, type: Number, enum: [0, 1], description: 'Filter by isUsed (0 or 1)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search by QR code, scratch code, SKU, or name (partial match)' })
  @ApiQuery({
    name: 'usedAtFrom',
    required: false,
    type: String,
    description: 'Filter codes used from this date (YYYY-MM-DD or ISO string)',
  })
  @ApiQuery({
    name: 'usedAtTo',
    required: false,
    type: String,
    description: 'Filter codes used until this date (YYYY-MM-DD or ISO string)',
  })
  @ApiResponse({
    status: 200,
    description: 'Excel file download',
    content: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - No data found for export',
  })
  public async exportProductQrCodes(
    @Res() res: FastifyReply,
    @Query('isUsed') isUsed?: number,
    @Query('search') search?: string,
    @Query('usedAtFrom') usedAtFrom?: string,
    @Query('usedAtTo') usedAtTo?: string,
  ): Promise<void> {
    const { buffer, fileName } = await this.pointManagementService.exportProductQrCodesToExcel(
      isUsed !== undefined ? Number(isUsed) : undefined,
      search,
      usedAtFrom,
      usedAtTo,
    );

    res
      .header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      .header('Content-Disposition', `attachment; filename="${fileName}"`)
      .send(buffer);
  }

  @Post('product-qr-codes/generate')
  @ApiOperation({
    summary: 'Generate new ProductQrCode records and export to CSV file (for data generation only, not database insertion)',
  })
  @ApiQuery({
    name: 'count',
    required: true,
    type: Number,
    example: 210000,
    description: 'Number of QR codes to generate (max 500,000)'
  })
  @ApiResponse({
    status: 200,
    description: 'CSV file download with generated QR codes',
    content: {
      'text/csv': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid count parameter',
  })
  public async generateProductQrCodes(
    @Res() res: FastifyReply,
    @Query('count') count: number,
  ): Promise<void> {
    const numericCount = Number(count);

    if (isNaN(numericCount)) {
      res.status(400).send({ message: 'Count must be a valid number' });
      return;
    }

    const { buffer, fileName } = await this.pointManagementService.generateProductQrCodes(numericCount);

    res
      .header('Content-Type', 'text/csv')
      .header('Content-Disposition', `attachment; filename="${fileName}"`)
      .send(buffer);
  }
}
