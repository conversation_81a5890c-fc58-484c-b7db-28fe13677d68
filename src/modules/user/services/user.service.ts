import { EntityManager } from '@mikro-orm/mysql';
import { Injectable } from '@nestjs/common';
import ExcelJS from 'exceljs';

import { UserErrorCode, UserRoleEnum } from '../../../common/enums/index.js';
import { BadRequestException } from '../../../common/exceptions/index.js';
import { UserRepository } from '../../../entities/index.js';
import type { UpdatePasswordDto, UserFilterFieldsDto } from '../dto/index.js';
import type { UserDto } from '../dto/user.dto.js';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly em: EntityManager,
  ) {}

  public async updatePassword(userId: number, dto: UpdatePasswordDto): Promise<void> {
    const user = await this.userRepository.findOneOrFail({ id: userId });

    const isOldPasswordMatch = await user.comparePassword(dto.oldPassword);
    if (!isOldPasswordMatch) {
      throw new BadRequestException({
        code: UserErrorCode.USER_OLD_PASSWORD_NOT_MATCH,
        message: 'Mật khẩu cũ không đúng',
      });
    }
    this.userRepository.assign(user, { password: dto.newPassword });
    await this.em.flush();
  }

  public async findUsers(dto: UserFilterFieldsDto, page = 1, limit = 10): Promise<{ users: UserDto[]; total: number }> {
    const { phone, name, gender, deleteFlag } = dto;

    const offset = (page - 1) * limit;

    const whereConditions: string[] = ['u.role = ?', 'u.delete_flag = ?'];
    const params: unknown[] = [UserRoleEnum.USER, deleteFlag ?? 0];

    if (phone) {
      whereConditions.push('u.phone LIKE ?');
      params.push(`%${phone}%`);
    }

    if (name) {
      whereConditions.push('u.full_name LIKE ?');
      params.push(`%${name}%`);
    }

    if (gender) {
      whereConditions.push('u.gender = ?');
      params.push(gender);
    }

    const sql = `
    SELECT 
      u.id,
      u.phone,
      u.email,
      u.full_name AS name,
      u.address,
      u.delete_flag AS deleteFlag,
      u.dob,
      u.gender,
      u.created_at AS createdAt,
      COALESCE(up.points, 0) AS points
    FROM users u
    LEFT JOIN user_point up ON u.id = up.user_id
    WHERE ${whereConditions.join(' AND ')}
    LIMIT ? OFFSET ?
  `;

    params.push(limit, offset);

    const users = await this.em.getConnection().execute(sql, params);

    const countSql = `
    SELECT COUNT(*) AS total
    FROM users u
    WHERE ${whereConditions.join(' AND ')}
  `;
    const totalParams = params.slice(0, params.length - 2); // Exclude limit and offset
    const totalResult = await this.em.getConnection().execute(countSql, totalParams);
    const total = totalResult[0]?.['total'] ?? 0;

    return {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      users: users.map((user: any) => ({
        id: user.id,
        phone: user.phone,
        points: user.points,
        email: user.email,
        name: user.name,
        address: user.address,
        status: user.deleteFlag === 0 ? 1 : 0,
        dob: user.dob,
        gender: user.gender,
        createdAt: user.createdAt,
      })),
      total,
    };
  }

  public async exportUsersToExcel(dto: UserFilterFieldsDto): Promise<{ buffer: Buffer; fileName: string }> {
    const { phone, name, gender, deleteFlag } = dto;

    const whereConditions: string[] = ['u.role = ?', 'u.delete_flag = ?'];
    const params: unknown[] = [UserRoleEnum.USER, deleteFlag ?? 0];

    if (phone) {
      whereConditions.push('u.phone LIKE ?');
      params.push(`%${phone}%`);
    }

    if (name) {
      whereConditions.push('u.full_name LIKE ?');
      params.push(`%${name}%`);
    }

    if (gender) {
      whereConditions.push('u.gender = ?');
      params.push(gender);
    }

    const sql = `
    SELECT 
      u.id,
      u.phone,
      u.email,
      u.full_name AS name,
      u.address,
      u.delete_flag AS deleteFlag,
      u.dob,
      u.gender,
      u.created_at AS createdAt,
      COALESCE(up.points, 0) AS points
    FROM users u
    LEFT JOIN user_point up ON u.id = up.user_id
    WHERE ${whereConditions.join(' AND ')}
    ORDER BY u.created_at DESC
  `;

    const users = await this.em.getConnection().execute(sql, params);

    if (users.length === 0) {
      throw new BadRequestException({
        code: UserErrorCode.USER_NOT_FOUND,
        message: 'No users found for export.',
      });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = `Users_Export_${timestamp}.xlsx`;

    const buffer = await this.generateUsersExcelFile(users);

    return { buffer, fileName };
  }

  private async generateUsersExcelFile(users: any[]): Promise<Buffer> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Users');

    worksheet.columns = [
      { header: 'No.', key: 'no', width: 8 },
      { header: 'ID', key: 'id', width: 10 },
      { header: 'Phone', key: 'phone', width: 15 },
      { header: 'Email', key: 'email', width: 25 },
      { header: 'Full Name', key: 'name', width: 25 },
      { header: 'Address', key: 'address', width: 30 },
      { header: 'Points', key: 'points', width: 12 },
      { header: 'Gender', key: 'gender', width: 10 },
      { header: 'Date of Birth', key: 'dob', width: 15 },
      { header: 'Status', key: 'status', width: 10 },
      { header: 'Created At', key: 'createdAt', width: 20 },
    ];

    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF4472C4' },
    };
    worksheet.getRow(1).font = { bold: true, color: { argb: 'FFFFFFFF' } };

    users.forEach((user, index) => {
      worksheet.addRow({
        no: index + 1,
        id: user.id,
        phone: user.phone ?? '',
        email: user.email ?? '',
        name: user.name ?? '',
        address: user.address ?? '',
        points: user.points ?? 0,
        gender: user.gender ?? '',
        dob: user.dob ?? '',
        status: user.deleteFlag === 0 ? 'Active' : 'Inactive',
        createdAt: user.createdAt ? new Date(user.createdAt).toISOString().split('T')[0] : '',
      });
    });

    worksheet.eachRow((row: ExcelJS.Row) => {
      row.eachCell((cell: ExcelJS.Cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });
    });

    worksheet.columns.forEach((column) => {
      if (column.header) {
        const lengths = users.map((row) => {
          const value = row[column.key as keyof typeof row];
          if (value === undefined || value === null) {
            return 0;
          }
          if (value instanceof Date) {
            return value.toISOString().length;
          }
          if (typeof value === 'string' || typeof value === 'number') {
            return String(value).length;
          }
          return 0;
        });
        const maxLength = Math.max(...lengths, String(column.header).length);
        column.width = Math.min(maxLength + 2, 50); // Max width of 50
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }
}
