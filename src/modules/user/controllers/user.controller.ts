import { Controller, Get, Put, Body, UseGuards, Query, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiBody } from '@nestjs/swagger';
import type { FastifyReply } from 'fastify';

import { GetUser } from '../../../common/decorators/index.js';
import { JwtAuthGuard } from '../../../common/guards/index.js';
import type { User } from '../../../common/interfaces/index.js';
import { UpdatePasswordDto, UserFilterFieldsDto } from '../dto/index.js';
import { UserService } from '../services/index.js';

@Controller('user')
@ApiTags('User')
@ApiBearerAuth('bearer')
@UseGuards(JwtAuthGuard)
export class UserController {
  // private readonly logger: Logger = new Logger(AuthController.name);

  constructor(private readonly userService: UserService) {}

  @Put('change-password')
  @ApiOperation({ summary: 'Change user password' })
  @ApiBody({ type: UpdatePasswordDto })
  @ApiResponse({ status: 200, description: 'Password successfully updated' })
  public async updatePassword(@GetUser() user: User, @Body() dto: UpdatePasswordDto): Promise<void> {
    await this.userService.updatePassword(user.id, dto);
  }

  @Get('')
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    schema: {
      properties: {
        users: {
          type: 'array',
          items: { type: 'object' },
        },
        total: { type: 'number' },
      },
    },
  })
  public async findUsers(
    @Query() dto: UserFilterFieldsDto,
    @Query('page') page: number,
    @Query('limit') limit: number,
  ): Promise<{ users: User[]; total: number }> {
    const { users, total } = await this.userService.findUsers(dto, page, limit);
    return { users, total };
  }

  @Get('export')
  @ApiOperation({
    summary: 'Export users list to Excel with same filtering options as the listing endpoint',
  })
  @ApiQuery({ name: 'name', required: false, type: String, description: 'Filter by user name (partial match)' })
  @ApiQuery({ name: 'phone', required: false, type: String, description: 'Filter by phone number (partial match)' })
  @ApiQuery({ name: 'gender', required: false, type: String, description: 'Filter by gender' })
  @ApiQuery({ name: 'deleteFlag', required: false, type: Number, description: 'Filter by delete flag (0: active, 1: deleted)' })
  @ApiResponse({
    status: 200,
    description: 'Excel file download',
    content: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - No users found for export',
  })
  public async exportUsers(
    @Res() res: FastifyReply,
    @Query() dto: UserFilterFieldsDto,
  ): Promise<void> {
    const { buffer, fileName } = await this.userService.exportUsersToExcel(dto);

    res
      .header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      .header('Content-Disposition', `attachment; filename="${fileName}"`)
      .send(buffer);
  }
}
