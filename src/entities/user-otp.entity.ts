import { Entity, Property, Index, EntityRepositoryType } from '@mikro-orm/core';
import { EntityRepository } from '@mikro-orm/mysql';

import { BaseEntity } from './base.entity.js';
import type { BooleanEnum } from '../common/enums/index.js';

@Entity({ tableName: 'user_otps', repository: () => UserOtpRepository })
@Index({ properties: ['userId', 'action', 'otp', 'isUsed'] })
@Index({ properties: ['userId', 'action', 'otp', 'isActive', 'expiresAt'] })
export class UserOtp extends BaseEntity {
  [EntityRepositoryType]?: UserOtpRepository;

  @Property()
  userId!: number; // Foreign key to the Users table

  @Property({ nullable: false, type: 'varchar', length: 30 })
  action!: number; // Action for which the OTP is generated (e.g., 'register', 'reset_password')

  @Property({ type: 'varchar', length: 6 })
  otp!: string; // The OTP code

  @Property({ type: 'tinyint', default: 1 })
  isActive?: BooleanEnum; // 0: Not active, 1: Active

  @Property({ type: 'datetime', columnType: 'timestamp', nullable: true })
  expiresAt!: Date; // Expiration time for the OTP

  @Property({ type: 'tinyint', default: 0 })
  isUsed?: BooleanEnum; // 0: Not used, 1: Used
}

export class UserOtpRepository extends EntityRepository<UserOtp> {
  // your custom methods...
}
