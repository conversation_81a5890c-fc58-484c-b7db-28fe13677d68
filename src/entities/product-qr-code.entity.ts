import { Entity, Property, Index, EntityRepositoryType, ManyToOne } from '@mikro-orm/core';
import { EntityRepository } from '@mikro-orm/mysql';

import { BaseEntity } from './base.entity.js';
import { User } from './user.entity.js';
import type { BooleanEnum } from '../common/enums/index.js';

@Entity({ tableName: 'product_qr_codes', repository: () => ProductQrCodeRepository })
@Index({ properties: ['qrCode', 'isUsed'] })
@Index({ properties: ['scratchCode', 'isUsed'] })
@Index({ properties: ['sku', 'isUsed'] })
@Index({ properties: ['name', 'isUsed'] })
@Index({ properties: ['sku', 'qrCode', 'scratchCode'] })
export class ProductQrCode extends BaseEntity {
  [EntityRepositoryType]?: ProductQrCodeRepository;

  @Property({ nullable: false, type: 'varchar', length: 500, unique: true })
  @Index()
  qrCode!: string;

  @Property({ nullable: false, type: 'varchar', length: 15, unique: true })
  @Index()
  scratchCode!: string;

  @Property({ nullable: true, type: 'varchar', length: 30 })
  @Index()
  sku?: string;

  @Property({ nullable: true, type: 'varchar', length: 250 })
  @Index()
  name?: string;

  @Property({ nullable: true, type: 'decimal' })
  price?: number;

  @Property({ nullable: true, type: 'varchar', length: 500 })
  manufacturer?: string;

  @Property({ nullable: true, type: 'varchar', length: 30 })
  dispatchDate?: string;

  @Property({ nullable: true, type: 'int' })
  point?: number;

  @Property({ type: 'tinyint', default: 0 })
  isUsed?: BooleanEnum; // 0: Not use, 1: used

  @Property({ nullable: true, type: 'datetime', columnType: 'timestamp' })
  usedAt?: Date;

  @Property({ nullable: true, type: 'varchar', length: 250 })
  batch?: string;

  @Property({ nullable: true, type: 'varchar', length: 30 })
  expiresAt?: string;

  @ManyToOne(() => User, { nullable: true, fieldName: 'customer_id' })
  customer?: User;
}

export class ProductQrCodeRepository extends EntityRepository<ProductQrCode> {}
