import { MySqlDriver } from '@mikro-orm/mysql';
import type { MikroOrmModuleOptions } from '@mikro-orm/nestjs';

export const config = {
  env: 'development',

  mikro: {
    debug: true,
    host: process.env['DB_HOST'] ?? '127.0.0.1',
    user: process.env['DB_USER'],
    password: process.env['DB_PASSWORD'],
    dbName: process.env['DB_NAME'],
    pool: {
      min: 0,
      max: 5,
      idleTimeoutMillis: 10000,
      acquireTimeoutMillis: 10000,
      destroyTimeoutMillis: 60000,
    },
  } satisfies MikroOrmModuleOptions<MySqlDriver>,

  awsRegion: process.env['AWS_REGION'],
  awsAccessKeyId: process.env['AWS_ACCESS_KEY_ID'],
  awsSecretAccessKey: process.env['AWS_SECRET_ACCESS_KEY'],
  s3UploadBucket: process.env['S3_UPLOAD_BUCKET'],
};
