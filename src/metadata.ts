/* eslint-disable */
export default async () => {
    const t = {
        ["./entities/user.entity.js"]: await import("./entities/user.entity.js"),
        ["./common/enums/user.enum.js"]: await import("./common/enums/user.enum.js"),
        ["./entities/user-otp.entity.js"]: await import("./entities/user-otp.entity.js"),
        ["./common/enums/boolean.enum.js"]: await import("./common/enums/boolean.enum.js"),
        ["./entities/product-point.entity.js"]: await import("./entities/product-point.entity.js"),
        ["./entities/product-qr-code.entity.js"]: await import("./entities/product-qr-code.entity.js"),
        ["./entities/user-point.entity.js"]: await import("./entities/user-point.entity.js"),
        ["./entities/redeemable-item.entity.js"]: await import("./entities/redeemable-item.entity.js"),
        ["./entities/redeemable-item-type.entity.js"]: await import("./entities/redeemable-item-type.entity.js"),
        ["./entities/user-point-history.entity.js"]: await import("./entities/user-point-history.entity.js"),
        ["./entities/user-redeemable-item.entity.js"]: await import("./entities/user-redeemable-item.entity.js"),
        ["./entities/redeemable-gift.entity.js"]: await import("./entities/redeemable-gift.entity.js"),
        ["./entities/redeemable-product.entity.js"]: await import("./entities/redeemable-product.entity.js"),
        ["./modules/redeemable-gift/dto/create-redeemable-gift.dto.js"]: await import("./modules/redeemable-gift/dto/create-redeemable-gift.dto.js"),
        ["./modules/redeemable-gift/dto/update-redeemable-gift.dto.js"]: await import("./modules/redeemable-gift/dto/update-redeemable-gift.dto.js"),
        ["./modules/redeemable-gift/dto/redeemable-gift.dto.js"]: await import("./modules/redeemable-gift/dto/redeemable-gift.dto.js"),
        ["./modules/point-management/dto/user-point-history.dto.js"]: await import("./modules/point-management/dto/user-point-history.dto.js"),
        ["./modules/product/dto/product-point-list.dto.js"]: await import("./modules/product/dto/product-point-list.dto.js")
    };
    return { "@nestjs/swagger": { "models": [[import("./modules/auth/dto/user-login.dto.js"), { "UserLoginDto": { email: { required: true, type: () => String }, password: { required: true, type: () => String } } }], [import("./entities/base.entity.js"), { "BaseEntity": { id: { required: true, type: () => Number }, createdAt: { required: true, type: () => Object }, updatedAt: { required: true, type: () => Object } } }], [import("./entities/user.entity.js"), { "User": { email: { required: false, type: () => String }, phone: { required: true, type: () => String }, password: { required: false, type: () => String }, fullName: { required: false, type: () => String }, avatar: { required: false, type: () => String }, address: { required: false, type: () => String }, dob: { required: false, type: () => String }, gender: { required: false, type: () => String }, status: { required: false, enum: t["./common/enums/user.enum.js"].UserStatusEnum }, phoneVerified: { required: false, type: () => Number }, emailVerified: { required: false, type: () => Number }, role: { required: false, enum: t["./common/enums/user.enum.js"].UserRoleEnum }, deleteFlag: { required: false, type: () => Number }, deletedAt: { required: false, type: () => Date } }, "UserRepository": {} }], [import("./entities/user-otp.entity.js"), { "UserOtp": { userId: { required: true, type: () => Number }, action: { required: true, type: () => Number }, otp: { required: true, type: () => String }, isActive: { required: false, enum: t["./common/enums/boolean.enum.js"].BooleanEnum }, expiresAt: { required: true, type: () => Date }, isUsed: { required: false, enum: t["./common/enums/boolean.enum.js"].BooleanEnum } }, "UserOtpRepository": {} }], [import("./entities/product-point.entity.js"), { "ProductPoint": { sku: { required: true, type: () => String }, name: { required: true, type: () => String }, price: { required: true, type: () => Number }, point: { required: false, type: () => Number }, updatedBy: { required: false, type: () => t["./entities/user.entity.js"].User }, updatedAt: { required: true, type: () => Object } }, "ProductPointRepository": {} }], [import("./entities/product-qr-code.entity.js"), { "ProductQrCode": { qrCode: { required: true, type: () => String }, scratchCode: { required: true, type: () => String }, sku: { required: false, type: () => String }, name: { required: false, type: () => String }, price: { required: false, type: () => Number }, manufacturer: { required: false, type: () => String }, dispatchDate: { required: false, type: () => String }, point: { required: false, type: () => Number }, isUsed: { required: false, enum: t["./common/enums/boolean.enum.js"].BooleanEnum }, usedAt: { required: false, type: () => Date }, batch: { required: false, type: () => String }, expiresAt: { required: false, type: () => String }, customer: { required: false, type: () => t["./entities/user.entity.js"].User } }, "ProductQrCodeRepository": {} }], [import("./entities/user-point.entity.js"), { "UserPoint": { id: { required: true, type: () => Number }, userId: { required: true, type: () => Number }, points: { required: true, type: () => Number }, updatedAt: { required: true, type: () => Object } }, "UserPointRepository": {} }], [import("./entities/redeemable-item.entity.js"), { "RedeemableItem": { type: { required: true, type: () => t["./entities/redeemable-item-type.entity.js"].RedeemableItemType }, name: { required: true, type: () => String }, description: { required: true, type: () => String }, thumbnail: { required: true, type: () => String }, point: { required: true, type: () => Number }, value: { required: true, type: () => Number } }, "RedeemableItemRepository": {} }], [import("./entities/redeemable-item-type.entity.js"), { "RedeemableItemType": { type: { required: true, type: () => String }, name: { required: true, type: () => String }, items: { required: true, type: () => Object } }, "RedeemableItemTypeRepository": {} }], [import("./entities/user-point-history.entity.js"), { "UserPointHistory": { userId: { required: true, type: () => Number }, itemName: { required: true, type: () => String }, points: { required: true, type: () => Number }, redeemableItemId: { required: false, type: () => Number }, productQrCodeId: { required: false, type: () => Number } }, "UserPointHistoryRepository": {} }], [import("./entities/user-redeemable-item.entity.js"), { "UserRedeemableItem": { userId: { required: true, type: () => Number }, redeemableItem: { required: true, type: () => t["./entities/redeemable-item.entity.js"].RedeemableItem }, code: { required: true, type: () => String }, isUsed: { required: false, enum: t["./common/enums/boolean.enum.js"].BooleanEnum } }, "UserRedeemableItemRepository": {} }], [import("./entities/redeemable-gift.entity.js"), { "RedeemableGift": { type: { required: true, type: () => String }, name: { required: true, type: () => String }, description: { required: false, type: () => String }, isActive: { required: true, type: () => Boolean }, products: { required: true, type: () => Object } }, "RedeemableGiftRepository": {} }], [import("./entities/redeemable-product.entity.js"), { "RedeemableVendureProduct": { redeemableGift: { required: false, type: () => t["./entities/redeemable-gift.entity.js"].RedeemableGift }, productId: { required: true, type: () => String }, variantId: { required: true, type: () => String }, variantName: { required: true, type: () => String }, variantPrice: { required: false, type: () => Number }, pointsCost: { required: true, type: () => Number }, imageUrl: { required: false, type: () => String }, sku: { required: false, type: () => String } }, "RedeemableVendureProductRepository": {} }], [import("./modules/point-management/dto/user-point-history.dto.js"), { "UserPointHistoryDto": { id: { required: true, type: () => Number }, userId: { required: true, type: () => Number }, itemName: { required: true, type: () => String }, points: { required: true, type: () => Number }, redeemableItemId: { required: false, type: () => Number }, productQrCodeId: { required: false, type: () => Number }, createdAt: { required: false, type: () => Object } } }], [import("./modules/point-management/dto/product-qr-code-list.dto.js"), { "ProductQrCodeListDto": { qrCode: { required: true, type: () => String }, scratchCode: { required: true, type: () => String }, sku: { required: false, type: () => String }, name: { required: false, type: () => String }, price: { required: false, type: () => Number }, dispatchDate: { required: false, type: () => String }, point: { required: false, type: () => Number }, isUsed: { required: false, type: () => Number }, batch: { required: false, type: () => String }, expiresAt: { required: false, type: () => String }, usedAt: { required: false, type: () => Date }, customer: { required: false, type: () => ({ phone: { required: true, type: () => String }, fullName: { required: false, type: () => String } }) } } }], [import("./modules/product/dto/product-point-list.dto.js"), { "ProductPointListDto": { sku: { required: true, type: () => String }, name: { required: true, type: () => String }, price: { required: true, type: () => Number }, point: { required: false, type: () => Number }, updatedBy: { required: false, type: () => ({ id: { required: true, type: () => Number }, fullName: { required: true, type: () => String } }), nullable: true }, updatedAt: { required: false, type: () => Date } } }], [import("./modules/redeemable-gift/dto/create-redeemable-gift.dto.js"), { "CreateRedeemableProductInGiftDto": { productId: { required: true, type: () => String, maxLength: 100 }, variantId: { required: true, type: () => String, maxLength: 100 }, variantName: { required: true, type: () => String, maxLength: 255 }, variantPrice: { required: false, type: () => Number }, pointsCost: { required: true, type: () => Number }, imageUrl: { required: false, type: () => String, maxLength: 500 }, sku: { required: false, type: () => String, maxLength: 100 } }, "CreateRedeemableGiftDto": { type: { required: true, type: () => String, maxLength: 30 }, name: { required: true, type: () => String, maxLength: 70 }, description: { required: false, type: () => String }, isActive: { required: false, type: () => Boolean }, products: { required: true, type: () => [t["./modules/redeemable-gift/dto/create-redeemable-gift.dto.js"].CreateRedeemableProductInGiftDto] } } }], [import("./modules/redeemable-gift/dto/update-redeemable-gift.dto.js"), { "UpdateRedeemableProductInGiftDto": { id: { required: false, type: () => Number }, productId: { required: true, type: () => String, maxLength: 100 }, variantId: { required: true, type: () => String, maxLength: 100 }, variantName: { required: true, type: () => String, maxLength: 255 }, variantPrice: { required: false, type: () => Number }, pointsCost: { required: true, type: () => Number }, imageUrl: { required: false, type: () => String, maxLength: 500 }, sku: { required: false, type: () => String, maxLength: 100 } }, "UpdateRedeemableGiftDto": { type: { required: false, type: () => String, maxLength: 30 }, name: { required: false, type: () => String, maxLength: 70 }, description: { required: false, type: () => String }, isActive: { required: false, type: () => Boolean }, products: { required: false, type: () => [t["./modules/redeemable-gift/dto/update-redeemable-gift.dto.js"].UpdateRedeemableProductInGiftDto] } } }], [import("./modules/redeemable-gift/dto/redeemable-gift.dto.js"), { "RedeemableProductInGiftDto": { id: { required: true, type: () => Number }, productId: { required: true, type: () => String }, variantId: { required: true, type: () => String }, variantName: { required: true, type: () => String }, variantPrice: { required: false, type: () => Number }, pointsCost: { required: true, type: () => Number }, imageUrl: { required: false, type: () => String }, sku: { required: false, type: () => String } }, "RedeemableGiftDto": { id: { required: true, type: () => Number }, type: { required: true, type: () => String }, name: { required: true, type: () => String }, description: { required: false, type: () => String }, isActive: { required: true, type: () => Boolean }, products: { required: true, type: () => [t["./modules/redeemable-gift/dto/redeemable-gift.dto.js"].RedeemableProductInGiftDto] } } }], [import("./modules/storage/dto/get-presigned.dto.js"), { "GetPresignedDto": { path: { required: true, type: () => String } } }], [import("./modules/user/dto/update-password.dto.js"), { "UpdatePasswordDto": { oldPassword: { required: true, type: () => String }, newPassword: { required: true, type: () => String } } }], [import("./modules/user/dto/user-filter-fields.dto.js"), { "UserFilterFieldsDto": { name: { required: false, type: () => String }, phone: { required: false, type: () => String }, gender: { required: false, type: () => String }, phoneVerified: { required: false, type: () => Number }, deleteFlag: { required: false, type: () => Number } } }], [import("./modules/user/dto/user.dto.js"), { "UserDto": { id: { required: true, type: () => Number }, phone: { required: true, type: () => String }, points: { required: false, type: () => Number }, email: { required: false, type: () => String }, name: { required: false, type: () => String }, address: { required: false, type: () => String }, status: { required: false, type: () => Number }, dob: { required: false, type: () => String }, gender: { required: false, type: () => String }, createdAt: { required: false, type: () => Date } } }]], "controllers": [[import("./modules/auth/controllers/auth.controller.js"), { "AuthController": { "login": {} } }], [import("./modules/health/health.controller.js"), { "HealthController": { "check": { type: Object } } }], [import("./modules/point-management/controllers/point-management.controller.js"), { "PointManagementController": { "getPointHistory": { type: [t["./modules/point-management/dto/user-point-history.dto.js"].UserPointHistoryDto] }, "getProductQrCodesWithSku": {}, "exportProductQrCodes": {} } }], [import("./modules/product/controllers/product.controller.js"), { "ProductController": { "find": {}, "updateProductPoint": { type: t["./modules/product/dto/product-point-list.dto.js"].ProductPointListDto }, "updateProduct": { type: t["./modules/product/dto/product-point-list.dto.js"].ProductPointListDto } } }], [import("./modules/redeemable-gift/controllers/redeemable-gift.controller.js"), { "RedeemableGiftController": { "findAll": {}, "create": { type: t["./modules/redeemable-gift/dto/redeemable-gift.dto.js"].RedeemableGiftDto }, "update": { type: t["./modules/redeemable-gift/dto/redeemable-gift.dto.js"].RedeemableGiftDto }, "delete": {}, "toggleActive": { type: t["./modules/redeemable-gift/dto/redeemable-gift.dto.js"].RedeemableGiftDto } } }], [import("./modules/storage/controllers/storage.controller.js"), { "StorageController": { "register": { type: String } } }], [import("./modules/user/controllers/user.controller.js"), { "UserController": { "updatePassword": {}, "findUsers": {}, "exportUsers": {} } }]] } };
};