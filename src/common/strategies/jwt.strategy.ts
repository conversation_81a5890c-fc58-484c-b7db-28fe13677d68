import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { UserService } from '../../modules/auth/services/index.js';
import { UserErrorCode } from '../enums/index.js';
import { BadRequestException } from '../exceptions/index.js';
import type { JwtPayload, User } from '../interfaces/index.js';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly userService: UserService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET,
    });
  }

  public async validate(payload: JwtPayload): Promise<User | null> {
    const user = await this.userService.findOne(payload.sub);
    if (user?.deleteFlag) {
      throw new BadRequestException({
        code: UserErrorCode.USER_IS_DELETED,
        message: '<PERSON><PERSON><PERSON> đăng nhập đã hết hạn',
      });
    }
    return user;
  }
}
