import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';

import { AuthService } from '../../modules/auth/services/index.js';
import type { User } from '../interfaces/index.js';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private auth: AuthService) {
    super({ usernameField: 'email' });
  }

  public validate(email: string, password: string): Promise<User> {
    return this.auth.validateUserLogin({ email, password });
  }
}
