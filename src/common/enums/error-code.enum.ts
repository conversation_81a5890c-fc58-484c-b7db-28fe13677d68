export enum CommonErrorCode {
  OPS_ERROR = 1,
}

export enum AuthErrorCode {
  INVALID_CREDENTIALS = 10,
  PHONE_ALREADY_REGISTERED = 11,
}

export enum UserErrorCode {
  USER_NOT_FOUND = 20,
  USER_ALREADY_EXISTS = 21,
  USER_NOT_ACTIVE = 22,
  USER_PHONE_NUMBER_NOT_VERIFIED = 23,
  USER_OLD_PASSWORD_NOT_MATCH = 24,
  USER_IS_DELETED = 25,
}

export enum OtpErrorCode {
  OTP_NOT_VALID_OR_EXPIRED = 30,
  OTP_EXPIRED = 31,
  OTP_PHONE_NUMBER_ALREADY_VERIFIED = 32,
}

export enum QrCodeErrorCode {
  QR_CODE_NOT_FOUND = 40,
  PRODUCT_NOT_CONFIGURED = 41,
  SCRATCH_CODE_INVALID = 42,
}

export enum RedeemableItemErrorCode {
  REDEEMABLE_ITEM_NOT_FOUND = 50,
  USER_POINT_NOT_ENOUGH = 51,
}
