import type { MikroORMOptions } from '@mikro-orm/core';
import { MySqlDriver } from '@mikro-orm/mysql';
import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Module, type MiddlewareConsumer, type NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ServeStaticModule } from '@nestjs/serve-static';
import { LoggerModule } from 'nestjs-pino';

import { CommonModule, LoggerContextMiddleware } from './common/index.js';
import { configuration, loggerOptions } from './config/index.js';
import { AuthModule } from './modules/auth/auth.module.js';
import { HealthModule } from './modules/health/health.module.js';
import { PointManagementModule } from './modules/point-management/point-management.module.js';
import { ProductModule } from './modules/product/product.module.js';
import { RedeemableGiftModule } from './modules/redeemable-gift/redeemable-gift.module.js';
import { StorageModule } from './modules/storage/storage.module.js';
import { UserModule } from './modules/user/user.module.js';
@Module({
  imports: [
    // https://github.com/iamolegga/nestjs-pino
    LoggerModule.forRoot(loggerOptions),
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    // Static
    ServeStaticModule.forRoot({
      rootPath: `${import.meta.dirname}/../public`,
    }),
    /**
     * https://docs.nestjs.com/recipes/mikroorm
     * https://mikro-orm.io/docs/usage-with-nestjs
     * https://mikro-orm.io
     */
    MikroOrmModule.forRootAsync({
      useFactory: (config: ConfigService) => config.getOrThrow<MikroORMOptions>('mikro'),
      inject: [ConfigService],
      driver: MySqlDriver,
    }),
    // Global
    CommonModule,
    // Terminus
    HealthModule,
    // Authentication
    AuthModule,
    //  User
    UserModule,
    StorageModule,
    PointManagementModule,
    ProductModule,
    RedeemableGiftModule,
  ],
  providers: [
    // {
    //   provide: APP_FILTER,
    //   useClass: ExceptionsFilter,
    // },
    // {
    //   provide: APP_PIPE,
    //   useValue: new ValidationPipe({
    //     transform: true,
    //     whitelist: true,
    //   }),
    // },
  ],
})
export class AppModule implements NestModule {
  // Global Middleware
  public configure(consumer: MiddlewareConsumer): void {
    consumer.apply(LoggerContextMiddleware).forRoutes('*');
  }
}
