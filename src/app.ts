import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, type NestFastifyApplication } from '@nestjs/platform-fastify';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger as PinoLogger } from 'nestjs-pino';

import { middleware } from './app.middleware.js';
import { AppModule } from './app.module.js';
import { GlobalExceptionFilter, ResponseInterceptor } from './common/index.js';
import { genReqId } from './config/index.js';
import metadata from './metadata.js';

async function bootstrap(): Promise<string> {
  const isProduction = process.env.NODE_ENV === 'production';

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter({
      trustProxy: isProduction,
      // Fastify has pino built in, but it use nestjs-pino, so we disable the logger.
      logger: false,
      // https://github.com/iamolegga/nestjs-pino/issues/1351
      genReqId,
    }),
    {
      bufferLogs: isProduction,
    },
  );

  app.useLogger(app.get(PinoLogger));
  // app.useGlobalInterceptors(new LoggerErrorInterceptor());
  app.useGlobalInterceptors(new ResponseInterceptor());
  app.useGlobalFilters(new GlobalExceptionFilter());
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
    }),
  );

  // app.setGlobalPrefix(`${process.env.SERVICE_NAME}/api`);

  app.enableCors({
    origin: ['http://localhost:3000', 'https://cms.dev.dstmall.net'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE', // Specify allowed HTTP methods
  });

  const options = new DocumentBuilder()
    .setTitle('OpenAPI Documentation')
    .setDescription('The sample API description')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  await SwaggerModule.loadPluginMetadata(metadata);
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup(`${process.env.SERVICE_NAME}/swagger`, app, document);

  // Fastify Middleware
  await middleware(app);
  app.enableShutdownHooks();

  const port = process.env.PORT || 3000;
  await app.listen(port, '0.0.0.0');

  return app.getUrl();
}

void (async () => {
  try {
    const url = await bootstrap();
    Logger.log(url, 'Bootstrap');
  } catch (error) {
    Logger.error(error, 'Bootstrap');
  }
})();
