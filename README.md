ở đây mới có 1 phư<PERSON>ng thức thanh to<PERSON>Config quân phải làm tiếp


# 2. <PERSON><PERSON><PERSON> nhập vào Amazon ECR
  * Chạy lệnh sau để đăng nhập vào Amazon ECR:
    * example:
    ```sh
    aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin xxxx.dkr.ecr.ap-southeast-1.amazonaws.com
    ```
    * ************ là AWS account ID của bạn.
    * ap-southeast-1 là region của bạn.

    * Thực thi lệnh sau để đăng nhập vào Amazon ECR:
    ```sh
      aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-southeast-1.amazonaws.com
    ```
# 3. Build image
  * Build image:
    ```bash
       docker build -t <repository>:<tag> .
    ```
  * <repository>: Tên của image hoặc repository mà bạn muốn gắn tag.
  * <tag>: Tag của image.

 docker buildx build --platform linux/amd64 -t ticketing-payment:1.0.6 --load . 
## 5. Push image lên registry Amazon ECR
  ```sh 
  docker tag worker-zone:1.0.0 <account_id>.dkr.ecr.<region>.amazonaws.com/worker-zone:1.0.0
  docker push <account_id>.dkr.ecr.<region>.amazonaws.com/worker-zone:1.0.0
  ```

  ```sh
  docker buildx build --platform linux/amd64 -t dstmall-cms:1.0.1 --load . 
  docker tag dstmall-cms:1.0.1 ************.dkr.ecr.ap-southeast-1.amazonaws.com/dstmall-cms:1.0.1
  docker push ************.dkr.ecr.ap-southeast-1.amazonaws.com/dstmall-cms:1.0.1
  ```

```sh
 docker buildx build --platform linux/amd64 -t pro-dstmall-cms:1.0.1 --load . 
 docker tag pro-dstmall-cms:1.0.1 ************.dkr.ecr.ap-southeast-1.amazonaws.com/pro/dstmall-cms:1.0.1
 docker push ************.dkr.ecr.ap-southeast-1.amazonaws.com/pro/dstmall-cms:1.0.1
```