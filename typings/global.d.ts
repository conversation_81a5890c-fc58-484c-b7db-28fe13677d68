import type { User } from '../src/common/interfaces/index.ts';

export declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: string;
      PORT: string;
      JWT_SECRET: string;
      SERVICE_NAME;
    }
  }
}

declare module 'http' {
  interface IncomingMessage {
    // customProps of pino-http
    customProps: object;
    // Request.prototype of fastify
    originalUrl: string;
  }
}

declare module 'fastify' {
  interface FastifyRequest {
    user: User;
  }
}
